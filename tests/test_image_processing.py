"""测试图片处理功能"""
import asyncio
import base64
from unittest.mock import Mock, AsyncMock
import pytest

from alphafi_agent.alphafi_graph import process_tool_message_with_artifacts, handle_image_analysis, has_image_artifact
from alphafi_agent.state import State
from langchain_core.messages import ToolMessage, HumanMessage


def create_mock_tool_message_with_image():
    """创建包含图片的模拟工具消息"""
    # 创建一个简单的base64编码图片数据（1x1像素的PNG）
    sample_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    mock_msg = Mock()
    mock_msg.content = "Retrieved 1 chunks:"
    mock_msg.tool_call_id = "test_call_id"
    mock_msg.name = "retrieve-chunks"
    mock_msg.artifact = [
        {
            "type": "image",
            "data": sample_image_data,
            "mimeType": "image/png",
            "annotations": None,
            "meta": None
        }
    ]
    
    return mock_msg


@pytest.mark.asyncio
async def test_process_tool_message_with_artifacts():
    """测试处理包含图片的工具消息"""
    # 创建包含图片的模拟消息
    mock_msg = create_mock_tool_message_with_image()
    
    # 处理消息
    processed_msg = await process_tool_message_with_artifacts(mock_msg)
    
    # 验证处理结果
    assert isinstance(processed_msg, ToolMessage)
    assert processed_msg.tool_call_id == "test_call_id"
    assert processed_msg.name == "retrieve-chunks"
    
    # 验证内容格式
    if isinstance(processed_msg.content, list):
        # 多模态内容格式
        assert len(processed_msg.content) == 2  # 文本 + 图片
        
        # 检查文本部分
        text_part = processed_msg.content[0]
        assert text_part["type"] == "text"
        assert text_part["text"] == "Retrieved 1 chunks:"
        
        # 检查图片部分
        image_part = processed_msg.content[1]
        assert image_part["type"] == "image_url"
        assert "data:image/png;base64," in image_part["image_url"]["url"]
    else:
        # 单一内容格式
        assert processed_msg.content == "Retrieved 1 chunks:"


@pytest.mark.asyncio
async def test_handle_image_analysis():
    """测试图片分析处理函数"""
    # 创建包含图片的状态
    processed_msg = ToolMessage(
        content=[
            {"type": "text", "text": "Retrieved DeFi protocol information:"},
            {
                "type": "image_url", 
                "image_url": {
                    "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                }
            }
        ],
        tool_call_id="test_call_id"
    )
    
    user_msg = HumanMessage(content="请分析这个DeFi协议的数据")
    
    state = State(messages=[user_msg, processed_msg])
    
    # 模拟LLM响应
    from alphafi_agent.alphafi_graph import llm
    original_ainvoke = llm.ainvoke
    
    async def mock_ainvoke(messages):
        mock_response = Mock()
        mock_response.content = "基于图片分析，这是一个DeFi协议的收益率数据图表..."
        return mock_response
    
    llm.ainvoke = mock_ainvoke
    
    try:
        # 执行图片分析
        result = await handle_image_analysis(state)
        
        # 验证结果
        assert "messages" in result
        assert len(result["messages"]) == 1
        assert "DeFi协议" in result["messages"][0].content
        
    finally:
        # 恢复原始方法
        llm.ainvoke = original_ainvoke


def test_image_data_format():
    """测试图片数据格式转换"""
    sample_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    mime_type = "image/png"

    # 构建预期的图片URL格式
    expected_url = f"data:{mime_type};base64,{sample_data}"

    # 验证格式正确
    assert expected_url.startswith("data:image/png;base64,")
    assert sample_data in expected_url


def test_has_image_artifact():
    """测试图片artifact检测函数"""
    from alphafi_agent.alphafi_graph import has_image_artifact

    # 测试字典格式
    dict_artifact = {"type": "image", "data": "test_data", "mimeType": "image/png"}
    assert has_image_artifact(dict_artifact) == True

    # 测试对象格式
    class MockImageArtifact:
        def __init__(self):
            self.type = "image"
            self.data = "test_data"
            self.mimeType = "image/png"

    obj_artifact = MockImageArtifact()
    assert has_image_artifact(obj_artifact) == True

    # 测试非图片类型
    non_image_artifact = {"type": "text", "data": "test_data"}
    assert has_image_artifact(non_image_artifact) == False


if __name__ == "__main__":
    # 运行基本测试
    asyncio.run(test_process_tool_message_with_artifacts())
    print("✅ 图片处理功能测试通过")

    asyncio.run(test_handle_image_analysis())
    print("✅ 图片分析功能测试通过")

    test_image_data_format()
    print("✅ 图片数据格式测试通过")

    test_has_image_artifact()
    print("✅ 图片artifact检测测试通过")

    print("\n🎉 所有测试都通过了！")
